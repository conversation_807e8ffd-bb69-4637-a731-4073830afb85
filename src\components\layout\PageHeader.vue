<template>
  <div class="page-header-wrapper">
    <el-page-header @back="goBack">
      <template #breadcrumb>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }"> 首页 </el-breadcrumb-item>
          <el-breadcrumb-item
            v-for="(item, index) in breadcrumbItems"
            :key="index"
            :to="index < breadcrumbItems.length - 1 ? item.path : undefined"
          >
            {{ item.label }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </template>
      <template #content>
        <span class="text-large font-600">{{ pageTitle }}</span>
      </template>
      <template v-if="hasExtraSlot" #extra>
        <slot name="extra"></slot>
      </template>

      <!-- 默认插槽用于页面描述等额外内容 -->
      <slot></slot>
    </el-page-header>
  </div>
</template>

<script setup>
import { computed, useSlots } from 'vue'
import { useRoute, useRouter } from 'vue-router'

defineOptions({
  name: 'PageHeader',
})

const route = useRoute()
const router = useRouter()
const slots = useSlots()

// 检查是否有extra插槽
const hasExtraSlot = computed(() => !!slots.extra)

// 页面标题
const pageTitle = computed(() => {
  return route.meta.title || '页面'
})

// 面包屑导航配置
const breadcrumbConfig = {
  '/bid-announcement': [
    { label: '竞价采购', path: '/bid-announcement' },
    { label: '竞价公告', path: '/bid-announcement' },
  ],
  '/change-announcement': [
    { label: '竞价采购', path: '/bid-announcement' },
    { label: '变更公告', path: '/change-announcement' },
  ],
  '/result-announcement': [
    { label: '竞价采购', path: '/bid-announcement' },
    { label: '结果公告', path: '/result-announcement' },
  ],
  '/announcement': [
    { label: '竞价采购', path: '/bid-announcement' },
    { label: '公告详情', path: route.path },
  ],
  '/supplier/bid-participation': [
    { label: '供应商中心', path: '/supplier' },
    { label: '竞价参与', path: '/supplier/bid-participation' },
  ],
  '/supplier/qualification': [
    { label: '供应商中心', path: '/supplier' },
    { label: '资格审核', path: '/supplier/qualification' },
  ],
  '/supplier/bid-history': [
    { label: '供应商中心', path: '/supplier' },
    { label: '竞价历史', path: '/supplier/bid-history' },
  ],
  '/purchaser/bid-management': [
    { label: '采购方中心', path: '/purchaser' },
    { label: '竞价管理', path: '/purchaser/bid-management' },
  ],
  '/purchaser/change-management': [
    { label: '采购方中心', path: '/purchaser' },
    { label: '变更管理', path: '/purchaser/change-management' },
  ],
  '/purchaser/result-management': [
    { label: '采购方中心', path: '/purchaser' },
    { label: '结果管理', path: '/purchaser/result-management' },
  ],
  '/admin/supplier-approval': [
    { label: '管理员中心', path: '/admin' },
    { label: '供应商审批', path: '/admin/supplier-approval' },
  ],
  '/admin/config-management': [
    { label: '管理员中心', path: '/admin' },
    { label: '系统配置', path: '/admin/config-management' },
  ],
  '/profile': [{ label: '个人中心', path: '/profile' }],
  '/settings': [{ label: '设置', path: '/settings' }],
  '/search': [{ label: '搜索结果', path: '/search' }],
}

// 面包屑项目
const breadcrumbItems = computed(() => {
  const currentPath = route.path

  // 处理动态路由（如公告详情页）
  if (currentPath.startsWith('/announcement/')) {
    return [
      { label: '竞价采购', path: '/bid-announcement' },
      { label: '公告详情', path: currentPath },
    ]
  }

  // 处理竞价详情页
  if (currentPath.startsWith('/purchaser/bid-detail/')) {
    return [
      { label: '采购方中心', path: '/purchaser' },
      { label: '竞价管理', path: '/purchaser/bid-management' },
      { label: '竞价详情', path: currentPath },
    ]
  }

  // 从配置中获取面包屑
  return (
    breadcrumbConfig[currentPath] || [
      { label: route.meta.title || '当前页面', path: currentPath },
    ]
  )
})

// 返回上一页
const goBack = () => {
  // 如果有历史记录，返回上一页
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    // 否则根据当前路径智能返回
    const currentPath = route.path

    if (currentPath.startsWith('/announcement/')) {
      router.push('/bid-announcement')
    } else if (currentPath.startsWith('/supplier/')) {
      router.push('/supplier/bid-participation')
    } else if (currentPath.startsWith('/purchaser/')) {
      router.push('/purchaser/bid-management')
    } else if (currentPath.startsWith('/admin/')) {
      router.push('/admin/supplier-approval')
    } else {
      router.push('/')
    }
  }
}
</script>

<style scoped>
.page-header-wrapper {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 16px;
  border-radius: 6px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}

/* 自定义面包屑样式 */
:deep(.el-breadcrumb) {
  font-size: 14px;
  line-height: 1.5;
}

:deep(.el-breadcrumb__item) {
  color: #666;
}

:deep(.el-breadcrumb__item:last-child .el-breadcrumb__inner) {
  color: #333;
  font-weight: 500;
}

/* 页面标题样式 */
.text-large {
  font-size: 20px;
  line-height: 28px;
}

.font-600 {
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header-wrapper {
    padding: 12px 16px;
    margin-bottom: 12px;
  }

  .text-large {
    font-size: 18px;
    line-height: 24px;
  }

  :deep(.el-breadcrumb) {
    font-size: 13px;
  }
}
</style>
