<template>
  <div class="app-layout">
    <YdHeader />
    <main class="flex-1 bg-gray-50 main-content">
      <!-- 页头组件和内容在同一个card中的页面 -->
      <template v-if="shouldUseUnifiedCard">
        <el-card class="unified-page-card">
          <PageHeader v-if="shouldShowPageHeader" :show-wrapper="false" />
          <router-view @update-pagination="handlePaginationUpdate" />
          <!-- 统一分页组件 -->
          <TablePagination
            v-if="paginationData.total > 0"
            :total="paginationData.total"
            @page-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </el-card>
      </template>
      <!-- 普通页面布局 -->
      <template v-else>
        <PageHeader v-if="shouldShowPageHeader" />
        <router-view />
      </template>
    </main>
    <!-- <AppFooter /> -->
  </div>
</template>

<script setup>
import { computed, reactive, provide } from 'vue'
import { useRoute } from 'vue-router'
import YdHeader from './Header.vue'
import PageHeader from './PageHeader.vue'
import TablePagination from '@/components/common/TablePagination.vue'
// import AppFooter from './Footer.vue'

defineOptions({
  name: 'AppLayout',
})

const route = useRoute()

// 分页数据
const paginationData = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 10,
})

// 分页事件回调
let pageChangeCallback = null
let sizeChangeCallback = null

// 计算是否使用统一的card布局
const shouldUseUnifiedCard = computed(() => {
  // 搜索结果页面使用统一card布局
  if (route.path === '/search' || route.name === 'SearchResults') {
    return true
  }

  // 可以在这里添加其他需要统一card布局的页面
  // if (route.path === '/other-page') {
  //   return true
  // }

  return false
})

// 处理分页数据更新
const handlePaginationUpdate = (data) => {
  if (data.total !== undefined) {
    paginationData.total = data.total
  }
  if (data.currentPage !== undefined) {
    paginationData.currentPage = data.currentPage
  }
  if (data.pageSize !== undefined) {
    paginationData.pageSize = data.pageSize
  }
  if (data.onPageChange) {
    pageChangeCallback = data.onPageChange
  }
  if (data.onSizeChange) {
    sizeChangeCallback = data.onSizeChange
  }
}

// 处理页面变化
const handlePageChange = (page, size) => {
  paginationData.currentPage = page
  pageChangeCallback?.(page, size)
}

// 处理每页条数变化
const handleSizeChange = (size, page) => {
  paginationData.pageSize = size
  paginationData.currentPage = page
  sizeChangeCallback?.(size, page)
}

// 提供分页控制方法给子组件
provide('pagination', {
  updatePagination: handlePaginationUpdate,
  paginationData,
})

// 计算是否显示页头组件
const shouldShowPageHeader = computed(() => {
  // 如果路由meta中明确设置了hidePageHeader为true，则不显示
  if (route.meta.hidePageHeader) {
    return false
  }

  // 登录和注册页面不显示页头
  if (route.meta.hideHeader) {
    return false
  }

  // 首页不显示页头
  if (route.path === '/') {
    return false
  }

  // 其他页面默认显示页头
  return true
})
</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  flex: 1;
  padding: 20px 1%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.unified-page-card {
  margin-bottom: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

:deep(.unified-page-card .el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 20px;
}
</style>
