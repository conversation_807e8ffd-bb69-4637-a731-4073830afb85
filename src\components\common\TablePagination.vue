<template>
  <div class="table-pagination">
    <div class="pagination-info">
      <span class="total-info">共 {{ total }} 条数据</span>
    </div>
    <div class="pagination-controls">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="layout"
        :background="background"
        :small="small"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

defineOptions({
  name: 'TablePagination',
})

// Props
const props = defineProps({
  total: {
    type: Number,
    default: 0,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  layout: {
    type: String,
    default: 'sizes, prev, pager, next, jumper',
  },
  background: {
    type: Boolean,
    default: true,
  },
  small: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['update:currentPage', 'update:pageSize', 'size-change', 'current-change'])

// 处理页面大小变化
const handleSizeChange = (size) => {
  emit('update:pageSize', size)
  emit('size-change', size)
}

// 处理当前页变化
const handleCurrentChange = (page) => {
  emit('update:currentPage', page)
  emit('current-change', page)
}
</script>

<style scoped>
.table-pagination {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 56px;
}

.pagination-info {
  flex-shrink: 0;
}

.total-info {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  line-height: 32px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

/* 分页组件样式优化 */
:deep(.el-pagination) {
  .el-pagination__total {
    margin-right: 16px;
    font-weight: 500;
  }
  
  .el-pagination__sizes {
    margin-right: 16px;
  }
  
  .el-pagination__jump {
    margin-left: 16px;
  }
  
  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }
  
  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-pagination {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
    width: 100%;
    justify-content: center;
  }
}
</style>
