<template>
  <div>
    <el-form
      ref="formRef"
      :model="formData"
      :label-width="labelWidth"
      class="search-form-content"
    >
      <el-row :gutter="24">
        <!-- 查询项区域，占85%宽度 -->
        <el-col :span="20" class="fields-area">
          <el-row :gutter="24">
            <!-- 表单字段，每行3个 -->
            <el-col
              v-for="field in fields"
              :key="field.name"
              :span="8"
              class="form-col"
            >
              <el-form-item
                :label="field.label"
                :prop="field.name"
                :required="field.required"
              >
                <!-- 文本输入框 -->
                <el-input
                  v-if="field.type === 'text'"
                  v-model="formData[field.name]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :clearable="field.clearable !== false"
                  :disabled="field.disabled"
                />

                <!-- 文本域 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  v-model="formData[field.name]"
                  type="textarea"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :clearable="field.clearable !== false"
                  :disabled="field.disabled"
                  :rows="field.rows || 3"
                />

                <!-- 选择器 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formData[field.name]"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :clearable="field.clearable !== false"
                  :disabled="field.disabled"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>

                <!-- 日期选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'date'"
                  v-model="formData[field.name]"
                  type="date"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :clearable="field.clearable !== false"
                  :disabled="field.disabled"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />

                <!-- 日期范围选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'daterange'"
                  v-model="formData[field.name]"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :clearable="field.clearable !== false"
                  :disabled="field.disabled"
                  style="width: 100%"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />

                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="field.type === 'number'"
                  v-model="formData[field.name]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="field.disabled"
                  :min="field.min"
                  :max="field.max"
                  :step="field.step || 1"
                  style="width: 100%"
                />

                <!-- 树形选择器 -->
                <el-tree-select
                  v-else-if="field.type === 'tree'"
                  v-model="formData[field.name]"
                  :data="field.datasrc"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :clearable="field.clearable !== false"
                  :disabled="field.disabled"
                  style="width: 100%"
                  check-strictly
                  :render-after-expand="false"
                  :props="{
                    label: 'label',
                    value: 'value',
                    children: 'children',
                  }"
                />

                <!-- 级联选择器 -->
                <el-cascader
                  v-else-if="field.type === 'cascader'"
                  v-model="formData[field.name]"
                  :options="field.options"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :clearable="field.clearable !== false"
                  :disabled="field.disabled"
                  style="width: 100%"
                  :props="field.props"
                />

                <!-- 多选框组 -->
                <el-checkbox-group
                  v-else-if="field.type === 'checkbox'"
                  v-model="formData[field.name]"
                  :disabled="field.disabled"
                >
                  <el-checkbox
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-checkbox>
                </el-checkbox-group>

                <!-- 单选框组 -->
                <el-radio-group
                  v-else-if="field.type === 'radio'"
                  v-model="formData[field.name]"
                  :disabled="field.disabled"
                >
                  <el-radio
                    v-for="option in field.options"
                    :key="option.value"
                    :label="option.value"
                  >
                    {{ option.label }}
                  </el-radio>
                </el-radio-group>

                <!-- 自定义插槽 -->
                <slot
                  v-else-if="field.type === 'slot'"
                  :name="field.name"
                  :field="field"
                  :value="formData[field.name]"
                  :set-value="(val) => (formData[field.name] = val)"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>

        <!-- 操作按钮区域，占15%宽度 -->
        <el-col v-if="showActions" :span="4" class="action-col">
          <div class="action-buttons">
            <el-button type="primary" class="search-btn" @click="handleSearch">
              {{ searchText }}
            </el-button>
            <el-button class="reset-btn" @click="handleReset">
              {{ resetText }}
            </el-button>
            <slot name="actions" :form-data="formData" />
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'

defineOptions({
  name: 'YdSearchForm',
})

// Props
const props = defineProps({
  // 表单字段配置数组
  fields: {
    type: Array,
    required: true,
  },
  // 表单数据
  modelValue: {
    type: Object,
    default: () => ({}),
  },
  // 标签宽度
  labelWidth: {
    type: String,
    default: '80px',
  },
  // 是否显示操作按钮
  showActions: {
    type: Boolean,
    default: true,
  },
  // 搜索按钮文本
  searchText: {
    type: String,
    default: '查询',
  },
  // 重置按钮文本
  resetText: {
    type: String,
    default: '重置',
  },
})

// Emits
const emit = defineEmits(['update:modelValue', 'search', 'reset'])

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({})

// 初始化表单数据
const initFormData = () => {
  // 清空现有数据
  Object.keys(formData).forEach((key) => {
    delete formData[key]
  })

  // 设置默认值
  props.fields.forEach((field) => {
    if (field.defaultValue !== undefined) {
      formData[field.name] = field.defaultValue
    } else {
      // 根据类型设置默认值
      switch (field.type) {
        case 'checkbox':
          formData[field.name] = []
          break
        case 'daterange':
          formData[field.name] = []
          break
        default:
          formData[field.name] = ''
      }
    }
  })

  // 合并传入的初始值
  Object.assign(formData, props.modelValue)
}

// 监听字段变化，重新初始化
watch(() => props.fields, initFormData, { immediate: true, deep: true })

// 监听外部数据变化
watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(formData, newVal)
  },
  { deep: true },
)

// 监听内部数据变化，向外发送
watch(
  formData,
  (newVal) => {
    emit('update:modelValue', { ...newVal })
  },
  { deep: true },
)

// 搜索
const handleSearch = () => {
  emit('search', { ...formData })
}

// 重置
const handleReset = () => {
  formRef.value?.resetFields()
  initFormData()
  emit('reset', { ...formData })
}

// 暴露方法
defineExpose({
  formRef,
  formData,
  handleSearch,
  handleReset,
})
</script>

<style scoped>
.search-form {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.search-form-content {
  margin: 0;
}

.fields-area {
  padding-right: 12px;
}

.form-col {
  margin-bottom: 16px;
}

.action-col {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-left: 12px;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  align-items: center;
}

.search-btn,
.reset-btn {
  width: 100%;
  max-width: 80px;
}

:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
