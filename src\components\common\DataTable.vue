<!-- eslint-disable vue/no-v-html -->
<!-- eslint-disable vue/no-unused-vars -->
<template>
  <el-table
    v-bind="$attrs"
    :data="data"
    :loading="loading"
    :row-style="rowStyle"
    @selection-change="handleSelectionChange"
    @row-click="handleRowClick"
  >
    <!-- 选择列 -->
    <el-table-column v-if="showSelection" type="selection" width="55" />

    <!-- 动态生成列 -->
    <el-table-column
      v-for="column in columns"
      :key="column.key || column.dataIndex"
      :prop="column.dataIndex"
      :label="column.title"
      :width="column.width"
      :min-width="column.minWidth"
      :fixed="column.fixed"
      :sortable="column.sortable"
      :align="column.align || 'left'"
    >
      <template #default="{ row, column: col, $index }">
        <!-- 自定义渲染插槽 -->
        <slot
          v-if="column.slot"
          :name="column.slot"
          :text="getColumnValue(row, column.dataIndex)"
          :record="row"
          :index="$index"
          :column="column"
        >
          {{ getColumnValue(row, column.dataIndex) }}
        </slot>

        <!-- 标签类型 -->
        <el-tag
          v-else-if="column.type === 'tag'"
          :type="
            getTagType(getColumnValue(row, column.dataIndex), column.tagMap)
          "
          size="small"
        >
          {{ getTagText(getColumnValue(row, column.dataIndex), column.tagMap) }}
        </el-tag>

        <!-- 链接类型 -->
        <el-link
          v-else-if="column.type === 'link'"
          type="primary"
          @click="handleLinkClick(row, column)"
        >
          <span
            v-html="highlightText(getColumnValue(row, column.dataIndex))"
          ></span>
        </el-link>

        <!-- 高亮文本 -->
        <span
          v-else-if="column.highlight"
          v-html="highlightText(getColumnValue(row, column.dataIndex))"
        ></span>

        <!-- 默认文本 -->
        <span v-else>
          {{ formatValue(getColumnValue(row, column.dataIndex), column) }}
        </span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
import { computed } from 'vue'

defineOptions({
  name: 'DataTable',
})

// Props
const props = defineProps({
  columns: {
    type: Array,
    required: true,
  },
  data: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  showSelection: {
    type: Boolean,
    default: false,
  },
  highlightKeyword: {
    type: String,
    default: '',
  },
  rowClickable: {
    type: Boolean,
    default: false,
  },
})

// Emits
const emit = defineEmits(['selection-change', 'row-click', 'link-click'])

// 行样式
const rowStyle = computed(() => {
  return props.rowClickable ? { cursor: 'pointer' } : {}
})

// 获取列值
const getColumnValue = (row, dataIndex) => {
  if (!dataIndex) return ''
  return dataIndex.split('.').reduce((obj, key) => obj?.[key], row) || ''
}

// 格式化值
const formatValue = (value, column) => {
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value)
  }
  return value
}

// 高亮文本
const highlightText = (text) => {
  if (!props.highlightKeyword || !text) return text
  const regex = new RegExp(`(${props.highlightKeyword})`, 'gi')
  return text.replace(regex, '<span class="keyword-highlight">$1</span>')
}

// 获取标签类型
const getTagType = (value, tagMap) => {
  if (!tagMap) return 'info'
  return tagMap[value]?.type || 'info'
}

// 获取标签文本
const getTagText = (value, tagMap) => {
  if (!tagMap) return value
  return tagMap[value]?.text || value
}

// 处理选择变化
const handleSelectionChange = (selection) => {
  emit('selection-change', selection)
}

// 处理行点击
const handleRowClick = (row, column, event) => {
  if (props.rowClickable) {
    emit('row-click', row, column, event)
  }
}

// 处理链接点击
const handleLinkClick = (row, column) => {
  emit('link-click', row, column)
}
</script>

<style scoped>
/* 高亮关键词样式 */
:deep(.keyword-highlight) {
  color: #ff4d4f;
  font-weight: 600;
  background-color: transparent;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
  &:hover {
    background-color: #f5f7fa !important;
  }
}
</style>
