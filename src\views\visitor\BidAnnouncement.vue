<template>
  <div class="bid-announcement-page">
    <!-- 搜索表单 -->
    <!-- <SearchForm
      v-model="searchForm"
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
    /> -->

    <!-- 数据表格 -->
    <DataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import SearchForm from '@/components/common/SearchForm.vue'
import DataTable from '@/components/common/DataTable.vue'
import { createField, COMMON_OPTIONS } from '@/types/searchForm.js'

defineOptions({
  name: 'BidAnnouncement',
})

// 搜索表单数据
const searchForm = ref({})

// 搜索字段配置
const searchFields = [
  createField.input('announcementNumber', '公告编号', {
    placeholder: '请输入公告编号',
    width: '200px',
  }),
  createField.input('title', '公告标题', {
    placeholder: '请输入公告标题',
    width: '200px',
  }),
  createField.select(
    'type',
    '公告类型',
    COMMON_OPTIONS.ANNOUNCEMENT_TYPE_OPTIONS,
    {
      width: '150px',
    },
  ),
  createField.select(
    'procurementMethod',
    '采购方式',
    COMMON_OPTIONS.PROCUREMENT_METHOD_OPTIONS,
    {
      width: '150px',
    },
  ),
  createField.dateRange('publishTime', '发布时间', {
    width: '300px',
  }),
  createField.select('status', '状态', COMMON_OPTIONS.STATUS_OPTIONS, {
    width: '120px',
  }),
]

// 表格列配置
const columns = [
  { prop: 'announcementNumber', label: '公告编号', width: 120 },
  { prop: 'title', label: '公告标题', minWidth: 200 },
  { prop: 'purchaser', label: '采购人', width: 150 },
  { prop: 'type', label: '公告类型', width: 100 },
  { prop: 'publishTime', label: '发布时间', width: 150 },
  { prop: 'registrationDeadline', label: '报名截止时间', width: 150 },
  { prop: 'status', label: '状态', width: 80 },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 模拟数据
const mockData = [
  {
    id: 1,
    announcementNumber: '*********',
    title: '某医院医疗设备采购项目招标公告',
    purchaser: 'XXX医院',
    type: '招标公告',
    publishTime: '2023-06-07 15:34:09',
    registrationDeadline: '2023-11-07 18:00:00',
    status: '有效',
  },
  {
    id: 2,
    announcementNumber: '*********',
    title: '学校教学设备采购项目招标公告',
    purchaser: 'XXX学校',
    type: '招标公告',
    publishTime: '2023-06-07 15:34:09',
    registrationDeadline: '2023-10-30 18:00:00',
    status: '已结束',
  },
]

// 搜索
const handleSearch = (formData) => {
  console.log('搜索参数:', formData)
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = (formData) => {
  console.log('重置后的数据:', formData)
  currentPage.value = 1
  loadData()
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))
    tableData.value = mockData
    total.value = mockData.length
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.bid-announcement-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 20px 20px 0 20px;
}
</style>
