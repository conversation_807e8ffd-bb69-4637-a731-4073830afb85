<template>
  <div class="bid-announcement-page">
    <!-- 搜索表单 -->
    <!-- 搜索表单 -->
    <YdSearchForm
      v-model="searchForm"
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :total="total"
      :current-page="currentPage"
      :page-size="pageSize"
      @page-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import YdSearchForm from '@/components/common/YdSearchForm.vue'
import DataTable from '@/components/common/DataTable.vue'
import {El}
defineOptions({
  name: 'BidAnnouncement',
})

// 搜索表单数据
const searchForm = ref({})

// 搜索字段配置
const searchFields = [
  {
    type: 'text',
    name: 'announcementNumber',
    label: '公告编号',
    placeholder: '请输入公告编号',
  },
  {
    type: 'text',
    name: 'title',
    label: '公告标题',
    placeholder: '请输入公告标题',
  },
  {
    type: 'select',
    name: 'type',
    label: '公告类型',
    placeholder: '请选择公告类型',
    options: [
      { label: '全部', value: '' },
      { label: '招标公告', value: 'tender' },
      { label: '中标公告', value: 'award' },
      { label: '变更公告', value: 'change' },
      { label: '废标公告', value: 'cancel' },
    ],
  },
  {
    type: 'select',
    name: 'procurementMethod',
    label: '采购方式',
    placeholder: '请选择采购方式',
    options: [
      { label: '全部', value: '' },
      { label: '公开招标', value: 'open' },
      { label: '邀请招标', value: 'invite' },
      { label: '竞争性谈判', value: 'negotiate' },
      { label: '单一来源', value: 'single' },
      { label: '询价', value: 'inquiry' },
    ],
  },
  {
    type: 'daterange',
    name: 'publishTime',
    label: '发布时间',
  },
  {
    type: 'select',
    name: 'status',
    label: '状态',
    placeholder: '请选择状态',
    options: [
      { label: '全部', value: '' },
      { label: '有效', value: '1' },
      { label: '无效', value: '0' },
    ],
  },
]

// 表格列配置
const columns = [
  { prop: 'announcementNumber', label: '公告编号', width: 120 },
  { prop: 'title', label: '公告标题', minWidth: 200 },
  { prop: 'purchaser', label: '采购人', width: 150 },
  { prop: 'type', label: '公告类型', width: 100 },
  { prop: 'publishTime', label: '发布时间', width: 150 },
  { prop: 'registrationDeadline', label: '报名截止时间', width: 150 },
  { prop: 'status', label: '状态', width: 80 },
]

// 表格数据
const tableData = ref([])
const loading = ref(false)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 模拟数据
const mockData = [
  {
    id: 1,
    announcementNumber: '*********',
    title: '某医院医疗设备采购项目招标公告',
    purchaser: 'XXX医院',
    type: '招标公告',
    publishTime: '2023-06-07 15:34:09',
    registrationDeadline: '2023-11-07 18:00:00',
    status: '有效',
  },
  {
    id: 2,
    announcementNumber: '*********',
    title: '学校教学设备采购项目招标公告',
    purchaser: 'XXX学校',
    type: '招标公告',
    publishTime: '2023-06-07 15:34:09',
    registrationDeadline: '2023-10-30 18:00:00',
    status: '已结束',
  },
]

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  loadData()
}

// 重置
const handleReset = () => {
  currentPage.value = 1
  loadData()
}

// 分页变化
const handlePageChange = (page) => {
  currentPage.value = page
  loadData()
}

// 页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadData()
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 500))
    tableData.value = mockData
    total.value = mockData.length
  } catch (error) {
    ElMessage.error(error)
  } finally {
    loading.value = false
  }
}

// 初始化
onMounted(() => {
  loadData()
})
</script>
