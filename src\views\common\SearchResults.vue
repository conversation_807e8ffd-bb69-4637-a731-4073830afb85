<template>
  <div class="search-results-page">
    <!-- 搜索结果描述 -->
    <div class="search-info">
      <p class="search-description">
        搜索关键词"<span class="keyword">{{ searchKeyword }}</span
        >"的结果，共找到 {{ pagination.total }} 条记录
      </p>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-row :gutter="16" class="filter-row">
        <el-col :span="4">
          <el-select
            v-model="filterForm.category"
            placeholder="公告类型"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="竞价公告" value="bid" />
            <el-option label="变更公告" value="change" />
            <el-option label="结果公告" value="result" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterForm.status"
            placeholder="项目状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="正在进行" value="active" />
            <el-option label="已结束" value="expired" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleFilter">
            <el-icon><Filter /></el-icon>
            筛选
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 结果头部 -->
    <div class="results-header">
      <span class="results-count">共 {{ pagination.total }} 条结果</span>
      <div class="sort-options">
        <span class="sort-label">排序：</span>
        <el-radio-group v-model="sortBy" @change="handleSort">
          <el-radio-button label="time">时间</el-radio-button>
          <el-radio-button label="relevance">相关性</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 结果列表 -->
    <div class="results-list">
      <el-table
        v-loading="loading"
        :data="searchResults"
        stripe
        style="width: 100%"
      >
        <el-table-column prop="serialNumber" label="序号" width="80" />
        <el-table-column prop="projectCode" label="项目编号" width="120" />
        <el-table-column prop="projectName" label="项目名称" min-width="200">
          <template #default="{ row }">
            <el-link
              type="primary"
              class="project-link"
              @click="viewDetail(row)"
            >
              <span v-html="highlightKeyword(row.projectName)"></span>
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="purchaser" label="采购单位" width="150">
          <template #default="{ row }">
            <span v-html="highlightKeyword(row.purchaser)"></span>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="公告类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getCategoryType(row.category)" size="small">
              {{ getCategoryText(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="180" />
        <el-table-column prop="deadline" label="截止时间" width="180" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Filter } from '@element-plus/icons-vue'

defineOptions({
  name: 'SearchResults',
})

const route = useRoute()
const router = useRouter()

// 搜索关键词
const searchKeyword = computed(() => route.query.keyword || '')

// 筛选表单
const filterForm = reactive({
  category: '',
  status: '',
  dateRange: null,
})

// 排序方式
const sortBy = ref('time')

// 搜索结果
const searchResults = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 模拟搜索结果数据
const mockSearchResults = [
  {
    id: 1,
    serialNumber: 1,
    projectCode: '**********',
    projectName: '消防水泵房设备采购',
    purchaser: 'XXXX医疗机构',
    category: 'bid',
    publishTime: '2022-06-07 15:34:09',
    deadline: '2022-11-07',
    status: 'active',
  },
  {
    id: 2,
    serialNumber: 2,
    projectCode: '**********',
    projectName: '巴马瑶族自治县医疗设备',
    purchaser: 'XXXXX民医院',
    category: 'change',
    publishTime: '2022-06-07 15:34:09',
    deadline: '2022-11-07',
    status: 'active',
  },
  // 可以添加更多模拟数据
]

// 高亮关键词
const highlightKeyword = (text) => {
  if (!searchKeyword.value || !text) return text
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi')
  return text.replace(regex, '<mark class="highlight">$1</mark>')
}

// 获取公告类型样式
const getCategoryType = (category) => {
  const typeMap = {
    bid: 'primary',
    change: 'warning',
    result: 'success',
  }
  return typeMap[category] || 'info'
}

// 获取公告类型文本
const getCategoryText = (category) => {
  const textMap = {
    bid: '竞价公告',
    change: '变更公告',
    result: '结果公告',
  }
  return textMap[category] || '未知'
}

// 获取状态样式
const getStatusType = (status) => {
  const typeMap = {
    active: 'success',
    pending: 'warning',
    expired: 'info',
    cancelled: 'danger',
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const textMap = {
    active: '正在进行',
    pending: '待开始',
    expired: '已结束',
    cancelled: '已取消',
  }
  return textMap[status] || '未知'
}

// 查看详情
const viewDetail = (row) => {
  const routeMap = {
    bid: '/announcement',
    change: '/announcement',
    result: '/announcement',
  }
  const basePath = routeMap[row.category] || '/announcement'
  router.push(`${basePath}/${row.id}`)
}

// 筛选
const handleFilter = () => {
  loadSearchResults()
}

// 排序
const handleSort = () => {
  loadSearchResults()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.pageSize = size
  loadSearchResults()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadSearchResults()
}

// 加载搜索结果
const loadSearchResults = () => {
  loading.value = true

  // 模拟API调用
  setTimeout(() => {
    // 根据关键词过滤结果
    let results = mockSearchResults
    if (searchKeyword.value) {
      results = mockSearchResults.filter(
        (item) =>
          item.projectName.includes(searchKeyword.value) ||
          item.purchaser.includes(searchKeyword.value) ||
          item.projectCode.includes(searchKeyword.value),
      )
    }

    // 应用筛选条件
    if (filterForm.category) {
      results = results.filter((item) => item.category === filterForm.category)
    }
    if (filterForm.status) {
      results = results.filter((item) => item.status === filterForm.status)
    }

    searchResults.value = results
    pagination.total = results.length
    loading.value = false
  }, 500)
}

// 监听路由变化，重新搜索
watch(
  () => route.query.keyword,
  () => {
    if (route.name === 'SearchResults') {
      loadSearchResults()
    }
  },
  { immediate: true },
)

onMounted(() => {
  loadSearchResults()
})
</script>

<style scoped>
.search-results-page {
  .search-info {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1890ff;

    .search-description {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;

      .keyword {
        color: #1890ff;
        font-weight: 600;
      }
    }
  }

  .filter-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid #f0f0f0;

    .filter-row {
      align-items: center;
    }
  }

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid #f0f0f0;

    .results-count {
      font-size: 14px;
      color: #666;
    }

    .sort-options {
      display: flex;
      align-items: center;

      .sort-label {
        margin-right: 8px;
        font-size: 14px;
        color: #666;
      }
    }
  }

  .results-list {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid #f0f0f0;
    padding: 16px;

    .project-link {
      font-weight: 500;

      &:hover {
        text-decoration: underline;
      }
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}

:deep(.highlight) {
  background-color: #fff3cd;
  color: #856404;
  padding: 0 2px;
  border-radius: 2px;
}

@media (max-width: 768px) {
  .search-results-page {
    .filter-section {
      .filter-row {
        .el-col {
          margin-bottom: 12px;
        }
      }
    }

    .results-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
}
</style>
