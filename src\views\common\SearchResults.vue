<template>
  <div class="search-results-page">
    <!-- 搜索结果描述 -->
    <div class="search-info">
      <p class="search-description">
        为你找到 {{ pagination.total }} 条记录，用时 {{ searchTime }}ms
      </p>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-row :gutter="16" class="filter-row">
        <el-col :span="4">
          <el-select
            v-model="filterForm.category"
            placeholder="公告类型"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="竞价公告" value="bid" />
            <el-option label="变更公告" value="change" />
            <el-option label="结果公告" value="result" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-select
            v-model="filterForm.status"
            placeholder="项目状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="正在进行" value="active" />
            <el-option label="已结束" value="expired" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-col>
        <el-col :span="4">
          <el-button type="primary" @click="handleFilter">
            <el-icon><Filter /></el-icon>
            筛选
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 结果头部 -->
    <div class="results-header">
      <span class="results-count">共 {{ pagination.total }} 条结果，用时 {{ searchTime }}ms</span>
      <div class="sort-options">
        <span class="sort-label">排序：</span>
        <el-radio-group v-model="sortBy" @change="handleSort">
          <el-radio-button label="time">时间</el-radio-button>
          <el-radio-button label="relevance">相关性</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <!-- 结果列表 -->
    <div class="results-list">
      <el-table
        v-loading="loading"
        :data="searchResults"
        stripe
        style="width: 100%"
        :row-style="{ cursor: 'pointer' }"
        @row-click="viewDetail"
      >
        <el-table-column prop="serialNumber" label="序号" width="80" />
        <el-table-column prop="announcementCode" label="公告编号" width="140" />
        <el-table-column prop="title" label="公告标题" min-width="300">
          <template #default="{ row }">
            <span
              class="announcement-title"
              v-html="highlightKeyword(row.title)"
            ></span>
          </template>
        </el-table-column>
        <el-table-column prop="purchaser" label="采购单位" width="180">
          <template #default="{ row }">
            <span v-html="highlightKeyword(row.purchaser)"></span>
          </template>
        </el-table-column>
        <el-table-column prop="purchaseType" label="采购类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getPurchaseTypeStyle(row.purchaseType)" size="small">
              {{ row.purchaseType }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="publishTime" label="发布时间" width="180" />
        <el-table-column prop="registrationDeadline" label="报名截止日期" width="180" />
        <el-table-column prop="registrationStatus" label="报名状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getRegistrationStatusStyle(row.registrationStatus)" size="small">
              {{ row.registrationStatus }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Filter } from '@element-plus/icons-vue'

defineOptions({
  name: 'SearchResults',
})

const route = useRoute()
const router = useRouter()

// 搜索关键词
const searchKeyword = computed(() => route.query.keyword || '')

// 筛选表单
const filterForm = reactive({
  category: '',
  status: '',
  dateRange: null,
})

// 排序方式
const sortBy = ref('time')

// 搜索结果
const searchResults = ref([])
const loading = ref(false)
const searchTime = ref(0) // 搜索用时

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
})

// 模拟搜索结果数据
const mockSearchResults = [
  {
    id: 1,
    serialNumber: 1,
    announcementCode: '**********',
    title: '消防水泵房设备采购竞价公告',
    purchaser: 'XXXX医疗机构',
    purchaseType: '货物采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07 18:00:00',
    registrationStatus: '正在报名',
  },
  {
    id: 2,
    serialNumber: 2,
    announcementCode: '**********',
    title: '巴马瑶族自治县医疗设备治疗仪采购',
    purchaser: 'XXXXX民医院',
    purchaseType: '设备采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07 18:00:00',
    registrationStatus: '正在报名',
  },
  {
    id: 3,
    serialNumber: 3,
    announcementCode: '**********',
    title: '康复治疗中心设备采购变更公告',
    purchaser: 'XXXX医疗机构',
    purchaseType: '设备采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-10-30 18:00:00',
    registrationStatus: '已截止',
  },
  {
    id: 4,
    serialNumber: 4,
    announcementCode: '**********',
    title: '分析治疗仪器设备采购',
    purchaser: 'XXXXX民医院',
    purchaseType: '仪器采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-10-30 18:00:00',
    registrationStatus: '已截止',
  },
  {
    id: 5,
    serialNumber: 5,
    announcementCode: '**********',
    title: '深度治疗设备采购结果公告',
    purchaser: 'XXXX医疗机构',
    purchaseType: '设备采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07 18:00:00',
    registrationStatus: '正在报名',
  },
]

// 高亮关键词 - 红色高亮
const highlightKeyword = (text) => {
  if (!searchKeyword.value || !text) return text
  const regex = new RegExp(`(${searchKeyword.value})`, 'gi')
  return text.replace(regex, '<span class="keyword-highlight">$1</span>')
}

// 获取采购类型样式
const getPurchaseTypeStyle = (type) => {
  const typeMap = {
    '货物采购': 'primary',
    '设备采购': 'success',
    '仪器采购': 'warning',
    '服务采购': 'info',
  }
  return typeMap[type] || 'info'
}

// 获取报名状态样式
const getRegistrationStatusStyle = (status) => {
  const statusMap = {
    '正在报名': 'success',
    '已截止': 'info',
  }
  return statusMap[status] || 'info'
}

// 查看详情
const viewDetail = (row) => {
  // 跳转到公告详情页
  router.push(`/announcement/${row.id}`)
}

// 筛选
const handleFilter = () => {
  loadSearchResults()
}

// 排序
const handleSort = () => {
  loadSearchResults()
}

// 分页大小改变
const handleSizeChange = (size) => {
  pagination.pageSize = size
  loadSearchResults()
}

// 当前页改变
const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadSearchResults()
}

// 加载搜索结果
const loadSearchResults = () => {
  loading.value = true
  const startTime = Date.now()

  // 模拟API调用
  setTimeout(() => {
    // 根据关键词过滤结果
    let results = mockSearchResults
    if (searchKeyword.value) {
      results = mockSearchResults.filter(
        (item) =>
          item.title.includes(searchKeyword.value) ||
          item.purchaser.includes(searchKeyword.value) ||
          item.announcementCode.includes(searchKeyword.value),
      )
    }

    // 应用筛选条件
    if (filterForm.category) {
      // 根据公告标题判断类型
      results = results.filter((item) => {
        if (filterForm.category === 'bid') {
          return item.title.includes('竞价公告')
        } else if (filterForm.category === 'change') {
          return item.title.includes('变更公告')
        } else if (filterForm.category === 'result') {
          return item.title.includes('结果公告')
        }
        return true
      })
    }
    if (filterForm.status) {
      results = results.filter((item) => {
        if (filterForm.status === 'active') {
          return item.registrationStatus === '正在报名'
        } else if (filterForm.status === 'expired') {
          return item.registrationStatus === '已截止'
        }
        return true
      })
    }

    // 按发布时间倒序排列
    results.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime))

    // 重新设置序号
    results.forEach((item, index) => {
      item.serialNumber = index + 1
    })

    searchResults.value = results
    pagination.total = results.length

    // 计算搜索用时
    searchTime.value = Date.now() - startTime

    loading.value = false
  }, 300)
}

// 监听路由变化，重新搜索
watch(
  () => route.query.keyword,
  () => {
    if (route.name === 'SearchResults') {
      loadSearchResults()
    }
  },
  { immediate: true },
)

onMounted(() => {
  loadSearchResults()
})
</script>

<style scoped>
.search-results-page {
  .search-info {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1890ff;

    .search-description {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;

      .keyword {
        color: #1890ff;
        font-weight: 600;
      }
    }
  }

  .filter-section {
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid #f0f0f0;

    .filter-row {
      align-items: center;
    }
  }

  .results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 16px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid #f0f0f0;

    .results-count {
      font-size: 14px;
      color: #666;
    }

    .sort-options {
      display: flex;
      align-items: center;

      .sort-label {
        margin-right: 8px;
        font-size: 14px;
        color: #666;
      }
    }
  }

  .results-list {
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    border: 1px solid #f0f0f0;
    padding: 16px;

    .announcement-title {
      font-weight: 500;
      color: #333;
      line-height: 1.5;
    }

    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
}
}

/* 红色高亮关键词样式 */
:deep(.keyword-highlight) {
  color: #ff4d4f;
  font-weight: 600;
  background-color: transparent;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa !important;
  }
}

@media (max-width: 768px) {
  .search-results-page {
    .filter-section {
      .filter-row {
        .el-col {
          margin-bottom: 12px;
        }
      }
    }

    .results-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
}
</style>
