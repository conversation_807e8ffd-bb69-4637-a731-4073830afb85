<template>
  <div class="search-results-page">
    <!-- 搜索结果描述 -->
    <div class="search-info">
      <p class="search-description">
        为你找到相关结果 {{ searchResults.length }} 条，用时约
        {{ searchTime }}ms
      </p>
    </div>

    <!-- 表格组件（集成分页） -->
    <div class="data-table-wrapper">
      <DataTable
        :columns="tableColumns"
        :data="searchResults"
        :loading="loading"
        :show-selection="true"
        :row-clickable="true"
        :highlight-keyword="searchKeyword"
        :show-pagination="true"
        :total="searchResults.length"
        :page-size="10"
        :current-page="1"
        stripe
        style="height: 100%"
        @selection-change="handleSelectionChange"
        @row-click="viewDetail"
        @link-click="handleLinkClick"
        @page-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import DataTable from '@/components/common/DataTable.vue'

defineOptions({
  name: 'SearchResults',
})

const route = useRoute()
const router = useRouter()

// 搜索关键词
const searchKeyword = computed(() => route.query.keyword || '')

// 筛选表单
const filterForm = reactive({
  category: '',
  status: '',
  dateRange: null,
})

// 排序方式

// 搜索结果
const searchResults = ref([])
const loading = ref(false)
const searchTime = ref(0) // 搜索用时
const selectedRows = ref([]) // 选中的行

// 表格列配置
const tableColumns = [
  {
    dataIndex: 'serialNumber',
    title: '序号',
    width: 80,
    key: 'serialNumber',
  },
  {
    dataIndex: 'announcementCode',
    title: '公告编号',
    width: 140,
    key: 'announcementCode',
  },
  {
    dataIndex: 'title',
    title: '公告标题',
    minWidth: 300,
    key: 'title',
    highlight: true, // 启用高亮
  },
  {
    dataIndex: 'purchaser',
    title: '采购单位',
    width: 180,
    key: 'purchaser',
    highlight: true, // 启用高亮
  },
  {
    dataIndex: 'purchaseType',
    title: '采购类型',
    width: 120,
    key: 'purchaseType',
    type: 'tag',
    tagMap: {
      货物采购: { type: 'primary', text: '货物采购' },
      设备采购: { type: 'success', text: '设备采购' },
      仪器采购: { type: 'warning', text: '仪器采购' },
      服务采购: { type: 'info', text: '服务采购' },
    },
  },
  {
    dataIndex: 'publishTime',
    title: '发布时间',
    width: 180,
    key: 'publishTime',
  },
  {
    dataIndex: 'registrationDeadline',
    title: '报名截止日期',
    width: 180,
    key: 'registrationDeadline',
  },
  {
    dataIndex: 'registrationStatus',
    title: '报名状态',
    width: 120,
    key: 'registrationStatus',
    type: 'tag',
    tagMap: {
      正在报名: { type: 'success', text: '正在报名' },
      已截止: { type: 'info', text: '已截止' },
    },
  },
]

// 模拟搜索结果数据
const mockSearchResults = [
  {
    id: 1,
    serialNumber: 1,
    announcementCode: '**********',
    title: '消防水泵房设备采购竞价公告',
    purchaser: 'XXXX医疗机构',
    purchaseType: '货物采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07 18:00:00',
    registrationStatus: '正在报名',
  },
  {
    id: 2,
    serialNumber: 2,
    announcementCode: '**********',
    title: '巴马瑶族自治县医疗设备治疗仪采购',
    purchaser: 'XXXXX民医院',
    purchaseType: '设备采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07 18:00:00',
    registrationStatus: '正在报名',
  },
  {
    id: 3,
    serialNumber: 3,
    announcementCode: '**********',
    title: '康复治疗中心设备采购变更公告',
    purchaser: 'XXXX医疗机构',
    purchaseType: '设备采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-10-30 18:00:00',
    registrationStatus: '已截止',
  },
  {
    id: 4,
    serialNumber: 4,
    announcementCode: '**********',
    title: '分析治疗仪器设备采购',
    purchaser: 'XXXXX民医院',
    purchaseType: '仪器采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-10-30 18:00:00',
    registrationStatus: '已截止',
  },
  {
    id: 5,
    serialNumber: 5,
    announcementCode: '**********',
    title: '深度治疗设备采购结果公告',
    purchaser: 'XXXX医疗机构',
    purchaseType: '设备采购',
    publishTime: '2022-06-07 15:34:09',
    registrationDeadline: '2022-11-07 18:00:00',
    registrationStatus: '正在报名',
  },
]

// 处理链接点击
const handleLinkClick = (row) => {
  viewDetail(row)
}

// 查看详情
const viewDetail = (row) => {
  // 跳转到公告详情页
  router.push(`/announcement/${row.id}`)
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 加载搜索结果
const loadSearchResults = () => {
  loading.value = true
  const startTime = Date.now()

  // 模拟API调用
  setTimeout(() => {
    // 根据关键词过滤结果
    let results = mockSearchResults
    if (searchKeyword.value) {
      results = mockSearchResults.filter(
        (item) =>
          item.title.includes(searchKeyword.value) ||
          item.purchaser.includes(searchKeyword.value) ||
          item.announcementCode.includes(searchKeyword.value),
      )
    }

    // 应用筛选条件
    if (filterForm.category) {
      // 根据公告标题判断类型
      results = results.filter((item) => {
        if (filterForm.category === 'bid') {
          return item.title.includes('竞价公告')
        } else if (filterForm.category === 'change') {
          return item.title.includes('变更公告')
        } else if (filterForm.category === 'result') {
          return item.title.includes('结果公告')
        }
        return true
      })
    }
    if (filterForm.status) {
      results = results.filter((item) => {
        if (filterForm.status === 'active') {
          return item.registrationStatus === '正在报名'
        } else if (filterForm.status === 'expired') {
          return item.registrationStatus === '已截止'
        }
        return true
      })
    }

    // 按发布时间倒序排列
    results.sort((a, b) => new Date(b.publishTime) - new Date(a.publishTime))

    // 重新设置序号
    results.forEach((item, index) => {
      item.serialNumber = index + 1
    })

    searchResults.value = results

    // 计算搜索用时
    searchTime.value = Date.now() - startTime

    loading.value = false
  }, 300)
}

// 监听路由变化，重新搜索
watch(
  () => route.query.keyword,
  () => {
    if (route.name === 'SearchResults') {
      loadSearchResults()
    }
  },
  { immediate: true },
)

onMounted(() => {
  loadSearchResults()
})
</script>

<style scoped>
.search-results-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 20px 20px 0 20px; /* 移除底部内边距，让表格卡片贴底 */

  .search-info {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1890ff;
    flex-shrink: 0;

    .search-description {
      margin: 0;
      color: #666;
      font-size: 14px;
      line-height: 1.5;

      .keyword {
        color: #1890ff;
        font-weight: 600;
      }
    }
  }

  .data-table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .announcement-title {
      font-weight: 500;
      color: #333;
      line-height: 1.5;
    }
  }
}

/* 红色高亮关键词样式 */
:deep(.keyword-highlight) {
  color: #ff4d4f;
  font-weight: 600;
  background-color: transparent;
}

/* 表格行悬停效果 */
:deep(.el-table__row) {
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa !important;
  }
}

/* 分页组件样式优化 */
:deep(.el-pagination) {
  .el-pagination__total {
    margin-right: 16px;
    font-weight: 500;
  }

  .el-pagination__sizes {
    margin-right: 16px;
  }

  .el-pagination__jump {
    margin-left: 16px;
  }

  .btn-prev,
  .btn-next {
    border-radius: 4px;
  }

  .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-results-page {
    .results-list {
      .pagination-section {
        flex-direction: column;
        gap: 12px;
        align-items: flex-start;

        .pagination-info {
          order: 2;
        }

        .pagination-controls {
          order: 1;
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}
</style>
